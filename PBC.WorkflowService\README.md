# PBC.WorkflowService

A .NET 8 Web API project for workflow services in the PBC microservices architecture.

## Configuration

### Ports
- **HTTP**: 5004

### Database
- **Connection String**: `Server=(localdb)\\mssqllocaldb;Database=WorkflowSharedAPI;Trusted_Connection=true;MultipleActiveResultSets=true`

### Service URLs
- **Core Service**: http://localhost:5001
- **Helpdesk Service**: http://localhost:5002
- **Utilities Service**: http://localhost:5003

## Features

- **Health Checks**: Available at `/health` and `/api/health`
- **Swagger Documentation**: Available at `/swagger` in development
- **CORS**: Configured to allow all origins, methods, and headers
- **HTTP Client**: Configured for inter-service communication

## Project Structure

```
PBC.WorkflowService/
├── Controllers/
│   ├── HealthController.cs
│   └── WorkflowAPIController.cs
├── Services/
│   ├── IHealthService.cs
│   ├── HealthService.cs
│   ├── IWorkflowAPIService.cs
│   └── WorkflowAPIService.cs
├── Models/
│   └── WorkflowAPIModels.cs
├── Utilities/
│   └── Utilities.cs
├── HttpClients/
├── Properties/
│   └── launchSettings.json
├── Program.cs
├── appsettings.json
├── appsettings.Development.json
└── PBC.WorkflowService.csproj
```

## Running the Application

```bash
cd PBC.WorkflowService
dotnet run
```

The application will be available at:
- HTTP: http://localhost:5004
- Swagger: http://localhost:5004/swagger

## API Endpoints

### Health Check Endpoints

- `GET /health` - Basic health check
- `GET /api/health` - Detailed health status
- `GET /api/health/status` - Service status information

### Workflow API Endpoints

#### 1. Insert Workflow History
- **Endpoint**: `POST /api/workflowapi/insert-workflow-history`
- **Description**: Insert workflow history for case progress tracking
- **Request Body**:
```json
{
  "connectionString": "string (optional)",
  "cpDetails": {
    "roleID": 0,
    "companyID": 0,
    "transactionNumber": 0,
    "workFlowID": 0,
    "currentStepID": 0,
    "actionID": 0,
    "actionBy": 0,
    "assignTo": 0,
    "nextStepID": 0
  },
  "smsCustomerObj": {
    "template_ID": 0,
    "param1": "string",
    "param2": "string",
    "param3": "string",
    "param4": "string"
  },
  "smsAssigneeObj": {
    "template_ID": 0,
    "param1": "string",
    "param2": "string",
    "param3": "string",
    "param4": "string"
  },
  "branchID": 0
}
```

#### 2. Check Invoke Child Object
- **Endpoint**: `POST /api/workflowapi/check-invoke-child-object`
- **Description**: Check if invoke child object is configured for workflow step
- **Request Body**:
```json
{
  "companyID": 0,
  "workFlowID": 0,
  "stepID": 0,
  "actionID": 0,
  "toStepID": 0
}
```

#### 3. Invoke Child Action
- **Endpoint**: `POST /api/workflowapi/invoke-child-action`
- **Description**: Get invoke child action details for workflow step link
- **Request Body**:
```json
{
  "stepLinkID": 0
}
```

#### 4. Local Time Conversion
- **Endpoint**: `POST /api/workflowapi/local-time`
- **Description**: Convert server time to local time based on branch timezone
- **Request Body**:
```json
{
  "connectionString": "string (optional)",
  "branchID": 0,
  "serverTime": "2023-12-01T10:00:00Z"
}
```

## Dependencies

The project includes the same comprehensive set of NuGet packages as the other PBC services:
- ASP.NET Core 8.0
- Entity Framework Core
- Swagger/OpenAPI
- JWT Authentication
- AWS SDK
- Azure Storage
- Excel processing (ClosedXML, EPPlus)
- PDF processing (iTextSharp)
- Web scraping (PuppeteerSharp, AngleSharp)
- And more...

## Development

This service is designed to handle workflow-related operations and can be extended with additional controllers and services as needed. The basic scaffold includes only health check functionality to get started.

## Inter-Service Communication

This service can communicate with other PBC microservices:
- PBC.CoreService (port 5001)
- PBC.HelpdeskService (port 5002)
- PBC.UtilityService (port 5003)

Use the configured HTTP client for making requests to other services.
