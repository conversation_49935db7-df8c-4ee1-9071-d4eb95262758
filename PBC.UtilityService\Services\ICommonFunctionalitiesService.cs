using Microsoft.AspNetCore.Mvc;
using PBC.UtilityService.Utilities.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PBC.UtilityService.Services
{
    /// <summary>
    /// Interface for Common Functionalities Service
    /// </summary>
    public interface ICommonFunctionalitiesService
    {
        /// <summary>
        /// Gets global resource object for localization
        /// </summary>
        Task<IActionResult> GetGlobalResourceObjectAsync(string cultureValue, string resourceKey);

        /// <summary>
        /// Gets resource string for localization
        /// </summary>
        Task<string> GetResourceStringAsync(string cultureValue, string resourceKey);

        /// <summary>
        /// Inserts GPS details
        /// </summary>
        Task<IActionResult> InsertGPSDetailsAsync(int companyId, int branchId, int userId, int objectId, int recordId, 
            double latitude, double longitude, string actionName, bool isFromMobile, int menuId, 
            DateTime? loggedInDate = null, DateTime? loggedDateTime = null);

        /// <summary>
        /// Converts server time to local time based on branch timezone
        /// </summary>
        Task<DateTime> LocalTimeAsync(int branchId, DateTime serverTime);

        /// <summary>
        /// Loads companies based on company type
        /// </summary>
        Task<IEnumerable<GNM_Company>> LoadCompanyAsync(string companyType, string connectionString);

        /// <summary>
        /// Loads branches for a company
        /// </summary>
        Task<IEnumerable<GNM_Branch>> LoadBranchAsync(bool active, int companyId, string connectionString);

        /// <summary>
        /// Loads branch locales for a company and language
        /// </summary>
        Task<IEnumerable<GNM_BranchLocale>> LoadBranchLocaleAsync(bool active, int companyId, int userLanguageId, string connectionString);

        /// <summary>
        /// Deletes attachments
        /// </summary>
        Task<string> DeleteAttachmentsAsync(Attachements[] attachments, string serverPath, string connectionString);

        /// <summary>
        /// Gets attachment count for an object
        /// </summary>
        Task<int> GetAttachmentCountAsync(int objectId, int transactionId, int detailId, string connectionString);

        /// <summary>
        /// Uploads attachments
        /// </summary>
        Task<List<Attachements>> UploadAttachmentAsync(Attachements[] attachments, int transactionId, int userId, int companyId, int detailId, string connectionString);

        /// <summary>
        /// Converts minutes to hours format
        /// </summary>
        string ConvertToHours(int minutes);

        /// <summary>
        /// Gets workflow role condition for user
        /// </summary>
        Task<string> GetGrpQinconditionAsync(int userId, string connectionString, int logException);

        /// <summary>
        /// Calculates working hours between dates
        /// </summary>
        Task<double> GetWorkingHoursAsync(DateTime? callDate, int companyId, string connectionString, int logException);

        /// <summary>
        /// Gets object ID by name
        /// </summary>
        Task<int> GetObjectIDAsync(string name, string connectionString);

        /// <summary>
        /// Checks if party-specific service level agreement exists
        /// </summary>
        Task<bool> CheckPartySpecificAsync(int partyId, int callComplexityId, int? callPriorityId, int companyId, string connectionString, int logException);

        /// <summary>
        /// Gets region name with localization support
        /// </summary>
        Task<string> GetRegionNameAsync(int userLanguageId, int generalLanguageId, int? branchId, string connectionString);

        /// <summary>
        /// Gets workflow status IDs for workflow management
        /// </summary>
        Task<string> GetStatusIDsAsync(int statusId, int workFlowId, string connectionString, int logException);

        /// <summary>
        /// Checks if user is admin for a workflow
        /// </summary>
        Task<bool> CheckForAdminAsync(int userId, string workFlowName, string dbName, string connectionString, int logException);
    }
}
