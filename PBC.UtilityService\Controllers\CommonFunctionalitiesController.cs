using Microsoft.AspNetCore.Mvc;
using PBC.UtilityService.Services;
using PBC.UtilityService.Utilities.DTOs;
using PBC.UtilityService.Utilities.Models;
using System.ComponentModel.DataAnnotations;

namespace PBC.UtilityService.Controllers
{
    /// <summary>
    /// Controller for Common Functionalities operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class CommonFunctionalitiesController : ControllerBase
    {
        private readonly ICommonFunctionalitiesService _commonFunctionalitiesService;
        private readonly ILogger<CommonFunctionalitiesController> _logger;

        public CommonFunctionalitiesController(
            ICommonFunctionalitiesService commonFunctionalitiesService,
            ILogger<CommonFunctionalitiesController> logger)
        {
            _commonFunctionalitiesService = commonFunctionalitiesService;
            _logger = logger;
        }

        /// <summary>
        /// Gets global resource object for localization
        /// </summary>
        /// <param name="request">Resource request</param>
        /// <returns>Resource value</returns>
        /// <response code="200">Returns the resource value</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("resource")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> GetGlobalResourceObject([FromBody] GetResourceRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/resource - Getting resource for culture: {Culture}", request.CultureValue);
                
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.GetGlobalResourceObjectAsync(request.CultureValue, request.ResourceKey);
                return (ActionResult)result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting resource for culture: {Culture}, key: {Key}", request.CultureValue, request.ResourceKey);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while retrieving resource");
            }
        }

        /// <summary>
        /// Gets resource string for localization
        /// </summary>
        /// <param name="request">Resource request</param>
        /// <returns>Resource string</returns>
        /// <response code="200">Returns the resource string</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("resource-string")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<string>> GetResourceString([FromBody] GetResourceRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/resource-string - Getting resource string for culture: {Culture}", request.CultureValue);
                
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.GetResourceStringAsync(request.CultureValue, request.ResourceKey);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting resource string for culture: {Culture}, key: {Key}", request.CultureValue, request.ResourceKey);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while retrieving resource string");
            }
        }

        /// <summary>
        /// Inserts GPS details
        /// </summary>
        /// <param name="request">GPS details request</param>
        /// <returns>Success result</returns>
        /// <response code="200">GPS details inserted successfully</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("gps-details")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> InsertGPSDetails([FromBody] InsertGPSDetailsRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/gps-details - Inserting GPS details for user: {UserId}", request.USER_ID);
                
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.InsertGPSDetailsAsync(
                    request.Company_ID, request.BranchID, request.USER_ID, request.OBJECT_ID, request.RECORD_ID,
                    request.LATITUDE, request.LONGITUDE, request.ActionName, request.IsFromMobile, request.Menu_ID,
                    request.LoggedINDate, request.LoggedDateTime);

                return (ActionResult)result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error inserting GPS details for user: {UserId}", request.USER_ID);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while inserting GPS details");
            }
        }

        /// <summary>
        /// Converts server time to local time
        /// </summary>
        /// <param name="request">Local time request</param>
        /// <returns>Local time</returns>
        /// <response code="200">Returns the local time</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("local-time")]
        [ProducesResponseType(typeof(DateTime), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<DateTime>> GetLocalTime([FromBody] LocalTimeRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/local-time - Converting time for branch: {BranchId}", request.BranchID);
                
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.LocalTimeAsync(request.BranchID, request.ServerTime);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting time for branch: {BranchId}", request.BranchID);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while converting time");
            }
        }

        /// <summary>
        /// Loads companies excluding specified type
        /// </summary>
        /// <param name="request">Load company request</param>
        /// <returns>List of companies</returns>
        /// <response code="200">Returns the list of companies</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("companies")]
        [ProducesResponseType(typeof(IEnumerable<GNM_Company>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<GNM_Company>>> LoadCompanies([FromBody] LoadCompanyRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/companies - Loading companies excluding type: {CompanyType}", request.CompanyType);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.LoadCompanyAsync(request.CompanyType, request.ConnectionString);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading companies excluding type: {CompanyType}", request.CompanyType);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while loading companies");
            }
        }

        /// <summary>
        /// Loads branches for a company
        /// </summary>
        /// <param name="request">Load branch request</param>
        /// <returns>List of branches</returns>
        /// <response code="200">Returns the list of branches</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("branches")]
        [ProducesResponseType(typeof(IEnumerable<GNM_Branch>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<GNM_Branch>>> LoadBranches([FromBody] LoadBranchRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/branches - Loading branches for company: {CompanyId}", request.CompanyID);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.LoadBranchAsync(request.Active, request.CompanyID, request.ConnectionString);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading branches for company: {CompanyId}", request.CompanyID);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while loading branches");
            }
        }

        /// <summary>
        /// Loads branch locales for a company and language
        /// </summary>
        /// <param name="request">Load branch locale request</param>
        /// <returns>List of branch locales</returns>
        /// <response code="200">Returns the list of branch locales</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("branch-locales")]
        [ProducesResponseType(typeof(IEnumerable<GNM_BranchLocale>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<GNM_BranchLocale>>> LoadBranchLocales([FromBody] LoadBranchLocaleRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/branch-locales - Loading branch locales for company: {CompanyId}, language: {LanguageId}",
                    request.CompanyID, request.UserLanguageID);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.LoadBranchLocaleAsync(request.Active, request.CompanyID, request.UserLanguageID, request.ConnectionString);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading branch locales for company: {CompanyId}, language: {LanguageId}",
                    request.CompanyID, request.UserLanguageID);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while loading branch locales");
            }
        }

        /// <summary>
        /// Gets attachment count for an object
        /// </summary>
        /// <param name="request">Get attachment count request</param>
        /// <returns>Attachment count</returns>
        /// <response code="200">Returns the attachment count</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("attachment-count")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> GetAttachmentCount([FromBody] GetAttachmentCountRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/attachment-count - Getting attachment count for object: {ObjectId}", request.ObjectID);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.GetAttachmentCountAsync(request.ObjectID, request.TransactionID, request.DetailID, request.ConnectionString);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting attachment count for object: {ObjectId}", request.ObjectID);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while getting attachment count");
            }
        }

        /// <summary>
        /// Converts minutes to hours format
        /// </summary>
        /// <param name="request">Convert to hours request</param>
        /// <returns>Hours in HH:MM format</returns>
        /// <response code="200">Returns the hours format</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("convert-to-hours")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<string>> ConvertToHours([FromBody] ConvertToHoursRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/convert-to-hours - Converting {Minutes} minutes to hours", request.Minutes);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                await Task.Delay(1); // Simulate async operation
                var result = _commonFunctionalitiesService.ConvertToHours(request.Minutes);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting {Minutes} minutes to hours", request.Minutes);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while converting to hours");
            }
        }

        /// <summary>
        /// Gets workflow role condition for user
        /// </summary>
        /// <param name="request">Get workflow role condition request</param>
        /// <returns>Role condition string</returns>
        /// <response code="200">Returns the role condition</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("workflow-role-condition")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<string>> GetGrpQincondition([FromBody] GetGrpQinconditionRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/workflow-role-condition - Getting role condition for user: {UserId}", request.UserId);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.GetGrpQinconditionAsync(request.UserId, request.ConnectionString, request.LogException);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting workflow role condition for user: {UserId}", request.UserId);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while getting workflow role condition");
            }
        }

        /// <summary>
        /// Calculates working hours between dates
        /// </summary>
        /// <param name="request">Get working hours request</param>
        /// <returns>Working hours</returns>
        /// <response code="200">Returns the working hours</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("working-hours")]
        [ProducesResponseType(typeof(double), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<double>> GetWorkingHours([FromBody] GetWorkingHoursRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/working-hours - Calculating working hours for company: {CompanyId}", request.CompanyId);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.GetWorkingHoursAsync(request.CallDate, request.CompanyId, request.ConnectionString, request.LogException);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating working hours for company: {CompanyId}", request.CompanyId);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while calculating working hours");
            }
        }

        /// <summary>
        /// Gets object ID by name
        /// </summary>
        /// <param name="request">Get object ID request</param>
        /// <returns>Object ID</returns>
        /// <response code="200">Returns the object ID</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("object-id")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> GetObjectID([FromBody] GetObjectIDRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/object-id - Getting object ID for name: {Name}", request.Name);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.GetObjectIDAsync(request.Name, request.ConnectionString);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting object ID for name: {Name}", request.Name);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while getting object ID");
            }
        }

        /// <summary>
        /// Checks if party-specific service level agreement exists
        /// </summary>
        /// <param name="request">Check party specific request</param>
        /// <returns>True if party-specific SLA exists</returns>
        /// <response code="200">Returns the check result</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("check-party-specific")]
        [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<bool>> CheckPartySpecific([FromBody] CheckPartySpecificRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/check-party-specific - Checking party-specific SLA for party: {PartyId}", request.PartyId);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.CheckPartySpecificAsync(request.PartyId, request.CallComplexityId, request.CallPriorityId, request.CompanyId, request.ConnectionString, request.LogException);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking party-specific SLA for party: {PartyId}", request.PartyId);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while checking party-specific SLA");
            }
        }

        /// <summary>
        /// Gets region name with localization support
        /// </summary>
        /// <param name="request">Get region name request</param>
        /// <returns>Region name</returns>
        /// <response code="200">Returns the region name</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("region-name")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<string>> GetRegionName([FromBody] GetRegionNameRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/region-name - Getting region name for branch: {BranchId}", request.BranchId);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.GetRegionNameAsync(request.UserLanguageId, request.GeneralLanguageId, request.BranchId, request.ConnectionString);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting region name for branch: {BranchId}", request.BranchId);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while getting region name");
            }
        }

        /// <summary>
        /// Gets workflow status IDs for workflow management
        /// </summary>
        /// <param name="request">Get status IDs request</param>
        /// <returns>Status IDs string</returns>
        /// <response code="200">Returns the status IDs</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("status-ids")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<string>> GetStatusIDs([FromBody] GetStatusIDsRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/status-ids - Getting status IDs for workflow: {WorkFlowId}", request.WorkFlowId);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.GetStatusIDsAsync(request.StatusId, request.WorkFlowId, request.ConnectionString, request.LogException);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting status IDs for workflow: {WorkFlowId}", request.WorkFlowId);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while getting status IDs");
            }
        }

        /// <summary>
        /// Checks if user is admin for a workflow
        /// </summary>
        /// <param name="request">Check for admin request</param>
        /// <returns>True if user is admin</returns>
        /// <response code="200">Returns the admin check result</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("check-for-admin")]
        [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<bool>> CheckForAdmin([FromBody] CheckForAdminRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/check-for-admin - Checking admin for user: {UserId}, workflow: {WorkFlowName}", request.UserId, request.WorkFlowName);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.CheckForAdminAsync(request.UserId, request.WorkFlowName, request.DBName, request.ConnectionString, request.LogException);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking admin for user: {UserId}, workflow: {WorkFlowName}", request.UserId, request.WorkFlowName);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while checking admin status");
            }
        }

        /// <summary>
        /// Deletes attachments from database and file system
        /// </summary>
        /// <param name="request">Delete attachments request</param>
        /// <returns>Deletion result message</returns>
        /// <response code="200">Returns the deletion result</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("delete-attachments")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<string>> DeleteAttachments([FromBody] DeleteAttachmentsRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/delete-attachments - Deleting {Count} attachments", request.Attachments.Length);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.DeleteAttachmentsAsync(request.Attachments, request.ServerPath, request.ConnectionString);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting attachments");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while deleting attachments");
            }
        }

        /// <summary>
        /// Uploads attachments to database and file system
        /// </summary>
        /// <param name="request">Upload attachment request</param>
        /// <returns>List of uploaded attachments</returns>
        /// <response code="200">Returns the uploaded attachments</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("upload-attachment")]
        [ProducesResponseType(typeof(List<Attachements>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<List<Attachements>>> UploadAttachment([FromBody] UploadAttachmentRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/upload-attachment - Uploading {Count} attachments for transaction: {TransactionId}", request.Attachments.Length, request.TransactionId);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _commonFunctionalitiesService.UploadAttachmentAsync(request.Attachments, request.TransactionId, request.UserId, request.CompanyId, request.DetailId, request.ConnectionString);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading attachments for transaction: {TransactionId}", request.TransactionId);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while uploading attachments");
            }
        }
    }
}
