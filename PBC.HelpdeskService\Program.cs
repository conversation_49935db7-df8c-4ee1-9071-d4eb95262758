using PBC.HelpdeskService.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add HTTP client for inter-service communication
builder.Services.AddHttpClient();

// Add health checks
builder.Services.AddHealthChecks();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Register application services
builder.Services.AddScoped<IHealthService, HealthService>();
builder.Services.AddScoped<IUtilityServiceClient, UtilityServiceClient>();
builder.Services.AddScoped<ICoreServiceClient, CoreServiceClient>();
builder.Services.AddScoped<IWorkflowServiceClient, WorkflowServiceClient>();
builder.Services.AddScoped<IHelpDeskUserLandingPageServices, HelpDeskUserLandingPageServices>();
builder.Services.AddScoped<IHelpDeskServiceRequestServices, HelpDeskServiceRequestServices>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/health");

app.Run();
