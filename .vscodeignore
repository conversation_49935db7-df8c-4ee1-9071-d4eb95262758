# Build outputs
**/bin/
**/obj/
**/packages/
**/.vs/
**/TestResults/

# Node modules
**/node_modules/

# Temporary files
**/*.tmp
**/*.temp
**/*.cache
**/*.user
**/*.suo
**/*.sln.docstates

# Binary files
**/*.dll
**/*.pdb
**/*.exe
**/*.msi
**/*.cab

# Log files
**/*.log

# Database files
**/*.mdf
**/*.ldf
**/*.sdf

# Visual Studio files
**/.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Generated files
**/Generated_Code/
**/*Designer.cs
**/*.designer.cs
**/*.Designer.cs
**/*.generated.cs
**/*.g.cs
**/*.g.i.cs

# Entity Framework
**/*.edmx.diagram
**/*.Context.cs
**/*.Context.tt
**/*.tt

# Package files
**/*.nupkg
**/*.snupkg

# Backup files
**/*.bak
**/*.backup
**/Backup*/
**/UpgradeLog*.XML
**/UpgradeLog*.htm

# Web publish profiles
**/PublishProfiles/
**/publish/

# Azure Functions
**/bin/
**/obj/
local.settings.json

# Large media files
**/*.mp4
**/*.avi
**/*.mov
**/*.wmv
**/*.flv
**/*.webm
**/*.mkv
**/*.m4v
**/*.3gp
**/*.3g2
**/*.rm
**/*.swf
**/*.flv
