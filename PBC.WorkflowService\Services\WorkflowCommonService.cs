using PBC.WorkflowService.Models;
using PBC.WorkflowService.Utilities;

namespace PBC.WorkflowService.Services
{
    /// <summary>
    /// Service implementation for WorkflowCommon utility functions
    /// </summary>
    public class WorkflowCommonService : IWorkflowCommonService
    {
        private readonly ILogger<WorkflowCommonService> _logger;

        public WorkflowCommonService(ILogger<WorkflowCommonService> logger)
        {
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<bool> CheckPrefixSuffixAsync(int companyID, int branchID, string objectName, string dbName, string connectionString)
        {
            try
            {
                _logger.LogInformation("Checking prefix/suffix for Company: {CompanyID}, Branch: {BranchID}, Object: {ObjectName}, DB: {DbName}", 
                    companyID, branchID, objectName, dbName);

                await Task.Delay(1); // Simulate async operation

                return WorkflowCommon.CheckPreffixSuffix(companyID, branchID, objectName, dbName, connectionString);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking prefix/suffix for Company: {CompanyID}, Branch: {BranchID}, Object: {ObjectName}", 
                    companyID, branchID, objectName);
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<int> GetObjectIDAsync(string name, string dbName, string connectionString)
        {
            try
            {
                _logger.LogInformation("Getting object ID for Name: {Name}, DB: {DbName}", name, dbName);

                await Task.Delay(1); // Simulate async operation

                return WorkflowCommon.GetObjectID(name, dbName, connectionString);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting object ID for Name: {Name}, DB: {DbName}", name, dbName);
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<dynamic> GetRolesForActionsAsync(int wfCurrentStepID, int actionID, int transactionID, int companyID, string workFlowName, string dbName, int userLanguageID, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Getting roles for actions - WFCurrentStepID: {WFCurrentStepID}, ActionID: {ActionID}, CompanyID: {CompanyID}, WorkFlowName: {WorkFlowName}",
                    wfCurrentStepID, actionID, companyID, workFlowName);

                await Task.Delay(1); // Simulate async operation

                return WorkflowCommon.GetRolesForActions(wfCurrentStepID, actionID, transactionID, companyID, workFlowName, dbName, userLanguageID, connectionString, logException);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting roles for actions - WFCurrentStepID: {WFCurrentStepID}, ActionID: {ActionID}", wfCurrentStepID, actionID);
                throw;
            }
        }
    }
}
