# PBC Microservices Workspace

This workspace is optimized to load only the PBC.* microservice projects for faster VS Code performance.

## Quick Start

### Option 1: Use the Workspace File (Recommended)
1. Close VS Code
2. Open `PBC-Microservices.code-workspace` in VS Code
3. This will load only the PBC projects with optimized settings

### Option 2: Use the PBC Solution
1. In VS Code, open the Command Palette (Ctrl+Shift+P)
2. Type "OmniSharp: Select Project" 
3. Choose `PBC-Microservices.sln`

## What's Excluded

The following directories are excluded from indexing to improve performance:
- `HCLSoftware_DPC_API_Standalone/` - Legacy API project
- `SharedAPIClassLibrary_DC/` - Shared library (legacy)
- `WorkFlow/` - Workflow project (legacy)
- `FunctionApp_DPC/` - Azure Function App
- `AzureFunctionApp/` - Azure Function App
- `packages/` - NuGet packages folder
- `bin/` and `obj/` - Build output folders

## PBC Projects Included

- **PBC.CoreService** (Port 5001) - Core business logic
- **PBC.UtilityService** (Port 7003) - Utility functions
- **PBC.HelpdeskService** (Port 5002) - Helpdesk operations
- **PBC.AggregatorService** (Port 5004) - API aggregation
- **PBC.WorkflowService** (Port 5005) - Workflow management

## Performance Optimizations Applied

1. **Reduced project count**: Only 5 PBC projects instead of 8+ total projects
2. **Excluded large folders**: packages/, bin/, obj/, legacy projects
3. **Disabled heavy features**: Roslyn analyzers, inlay hints, auto-imports
4. **Optimized timeouts**: Reduced project load timeout to 30 seconds
5. **Limited results**: Max 25 project results instead of default 250

## Running Services

Use the provided PowerShell script:
```powershell
.\start-services.ps1
```

Or run individual services:
```bash
dotnet run --project PBC.UtilityService
dotnet run --project PBC.CoreService
dotnet run --project PBC.HelpdeskService
dotnet run --project PBC.AggregatorService
dotnet run --project PBC.WorkflowService
```

## Troubleshooting

If VS Code is still slow:
1. Restart VS Code completely
2. Clear OmniSharp cache: Ctrl+Shift+P → "OmniSharp: Restart OmniSharp"
3. Ensure you're using the workspace file or PBC-specific solution
4. Check that excluded folders are not being indexed in the Explorer panel
