using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PBC.WorkflowService.Models
{
    using System;
    using System.Collections.Generic;

    public class SMSTemplate
    {
        public int Template_ID { get; set; }
        public string Param1 { get; set; }
        public string Param2 { get; set; }
        public string Param3 { get; set; }
        public string Param4 { get; set; }
    }
    public partial class WF_Sms
    {
        public int Sms_ID { get; set; }
        public string Sms_Text { get; set; }
        public string Sms_Mobile_Number { get; set; }
        public Nullable<System.DateTime> Sms_Queue_Date { get; set; }
        public Nullable<System.DateTime> Sms_Sent_Date { get; set; }
        public bool Sms_SentStatus { get; set; }
        public int Template_ID { get; set; }
        public string Parameter1_value { get; set; }
        public string Parameter2_value { get; set; }
        public string Parameter3_value { get; set; }
        public string Parameter4_value { get; set; }
    }

    public partial class WF_WFField
    {
        public WF_WFField()
        {
            this.GNM_WFFieldValue = new HashSet<WF_WFFieldValue>();
        }

        public int WFField_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public string WorkFlowFieldName { get; set; }

        public virtual ICollection<WF_WFFieldValue> GNM_WFFieldValue { get; set; }
        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }
    }

    public partial class WF_WFCase_Progress
    {
        public int WFCaseProgress_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public int Transaction_ID { get; set; }
        public int WFSteps_ID { get; set; }
        public Nullable<int> Addresse_ID { get; set; }
        public byte Addresse_Flag { get; set; }
        public System.DateTime Received_Time { get; set; }
        public Nullable<int> Actioned_By { get; set; }
        public Nullable<System.DateTime> Action_Time { get; set; }
        public Nullable<int> Action_Chosen { get; set; }
        public string Action_Remarks { get; set; }
        public Nullable<bool> Locked_Ind { get; set; }
        public Nullable<int> WFNextStep_ID { get; set; }
    }
    public partial class WF_WFAction
    {
        public WF_WFAction()
        {
            this.GNM_WFStepLink = new HashSet<WF_WFStepLink>();
            this.GNM_WFActionLocale = new HashSet<WF_WFActionLocale>();
        }

        public int WFAction_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public string WFAction_Name { get; set; }
        public string ActionCode { get; set; }

        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }
        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }
        public virtual ICollection<WF_WFActionLocale> GNM_WFActionLocale { get; set; }
    }
    public partial class WF_WFRole
    {
        public WF_WFRole()
        {
            this.GNM_WFRoleUser = new HashSet<WF_WFRoleUser>();
            this.GNM_WFStepLink = new HashSet<WF_WFStepLink>();
            this.GNM_WFRoleLocale = new HashSet<WF_WFRoleLocale>();
        }

        public int WFRole_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public string WFRole_Name { get; set; }
        public bool WfRole_IsAdmin { get; set; }
        public bool WfRole_AutoAllocationAllowed { get; set; }
        public Nullable<bool> WFRole_IsRoleExternal { get; set; }
        public Nullable<int> WFRole_ExternalCompany_ID { get; set; }

        public virtual ICollection<WF_WFRoleUser> GNM_WFRoleUser { get; set; }
        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }
        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }
        public virtual ICollection<WF_WFRoleLocale> GNM_WFRoleLocale { get; set; }
    }

    public partial class WF_WFStepLink
    {
        public int WFStepLink_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public int Company_ID { get; set; }
        public int FrmWFSteps_ID { get; set; }
        public int WFAction_ID { get; set; }
        public int ToWFSteps_ID { get; set; }
        public Nullable<int> Addresse_WFRole_ID { get; set; }
        public byte Addresse_Flag { get; set; }
        public bool IsSMSSentToCustomer { get; set; }
        public bool IsEmailSentToCustomer { get; set; }
        public bool IsSMSSentToAddressee { get; set; }
        public bool IsEmailSentToAddresse { get; set; }
        public bool AutoAllocationAllowed { get; set; }
        public bool IsVersionEnabled { get; set; }
        public Nullable<int> InvokeParentWF_ID { get; set; }
        public Nullable<int> InvokeParentWFLink_ID { get; set; }
        public Nullable<int> InvokeChildObject_ID { get; set; }
        public Nullable<int> InvokeChildObjectAction { get; set; }
        public Nullable<int> WFField_ID { get; set; }
        public string AutoCondition { get; set; }

        public virtual WF_WFAction GNM_WFAction { get; set; }
        public virtual WF_WFRole GNM_WFRole { get; set; }
        public virtual WF_WFSteps GNM_WFSteps { get; set; }
        public virtual WF_WFSteps GNM_WFSteps1 { get; set; }
        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }
    }

    public partial class WF_WFSteps
    {
        public WF_WFSteps()
        {
            this.GNM_WFStepLink = new HashSet<WF_WFStepLink>();
            this.GNM_WFStepLink1 = new HashSet<WF_WFStepLink>();
            this.GNM_WFStepsLocale = new HashSet<WF_WFStepsLocale>();
        }

        public int WFSteps_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public string WFStep_Name { get; set; }
        public int WFStepType_ID { get; set; }
        public int WFStepStatus_ID { get; set; }
        public bool WFStep_IsActive { get; set; }
        public string BranchCode { get; set; }

        public virtual WF_WFStepType GNM_WFStepType { get; set; }
        public virtual WF_WFStepStatus GNM_WFStepStatus { get; set; }
        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }
        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }
        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink1 { get; set; }
        public virtual ICollection<WF_WFStepsLocale> GNM_WFStepsLocale { get; set; }
    }
    public partial class WF_WFStepType
    {
        public WF_WFStepType()
        {
            this.GNM_WFSteps = new HashSet<WF_WFSteps>();
        }

        public int WFStepType_ID { get; set; }
        public string WFStepType_Nm { get; set; }

        public virtual ICollection<WF_WFSteps> GNM_WFSteps { get; set; }
    }

    public partial class WF_WorkFlow
    {
        public WF_WorkFlow()
        {
            this.GNM_WFAction = new HashSet<WF_WFAction>();
            this.GNM_WFField = new HashSet<WF_WFField>();
            this.GNM_WFFieldValue = new HashSet<WF_WFFieldValue>();
            this.GNM_WFRole = new HashSet<WF_WFRole>();
            this.GNM_WFSteps = new HashSet<WF_WFSteps>();
            this.GNM_WFStepLink = new HashSet<WF_WFStepLink>();
            this.GNM_WFActionLocale = new HashSet<WF_WFActionLocale>();
        }

        public int WorkFlow_ID { get; set; }
        public string WorkFlow_Name { get; set; }
        public Nullable<bool> AllQueue_Filter_IsBranch { get; set; }

        public virtual ICollection<WF_WFAction> GNM_WFAction { get; set; }
        public virtual ICollection<WF_WFField> GNM_WFField { get; set; }
        public virtual ICollection<WF_WFFieldValue> GNM_WFFieldValue { get; set; }
        public virtual ICollection<WF_WFRole> GNM_WFRole { get; set; }
        public virtual ICollection<WF_WFSteps> GNM_WFSteps { get; set; }
        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }
        public virtual ICollection<WF_WFActionLocale> GNM_WFActionLocale { get; set; }
    }
    public partial class WF_WFFieldValue
    {
        public int WFFieldValue_ID { get; set; }
        public int WFField_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public int Company_ID { get; set; }
        public int Transaction_ID { get; set; }
        public string WorkFlowFieldValue { get; set; }

        public virtual WF_WFField GNM_WFField { get; set; }
        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }
    }

    public partial class WF_WFRoleUser
    {
        public int WFRoleUser_ID { get; set; }
        public int WFRole_ID { get; set; }
        public int UserID { get; set; }
        public int ApprovalLimit { get; set; }

        public virtual WF_WFRole GNM_WFRole { get; set; }
    }
    public partial class WorkFlowEntity : DbContext
    {
        public WorkFlowEntity(DbContextOptions<WorkFlowEntity> options) : base(options)
        {
        }

        public WorkFlowEntity(string connectionString) : base(GetOptions(connectionString))
        {
        }

        private static DbContextOptions<WorkFlowEntity> GetOptions(string connectionString)
        {
            var optionsBuilder = new DbContextOptionsBuilder<WorkFlowEntity>();
            optionsBuilder.UseSqlServer(connectionString);
            return optionsBuilder.Options;
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Configure entity relationships and constraints here if needed
            base.OnModelCreating(modelBuilder);
        }

        public DbSet<WF_Sms> WF_Sms { get; set; }
        public DbSet<WF_WFAction> WF_WFAction { get; set; }
        public DbSet<WF_WFCase_Progress> WF_WFCase_Progress { get; set; }
        public DbSet<WF_WFField> WF_WFField { get; set; }
        public DbSet<WF_WFFieldValue> WF_WFFieldValue { get; set; }
        public DbSet<WF_WFRole> WF_WFRole { get; set; }
        public DbSet<WF_WFRoleUser> WF_WFRoleUser { get; set; }
        public DbSet<WF_WFSteps> WF_WFSteps { get; set; }
        public DbSet<WF_WFStepStatus> WF_WFStepStatus { get; set; }
        public DbSet<WF_WFStepType> WF_WFStepType { get; set; }
        public DbSet<WF_WorkFlow> WF_WorkFlow { get; set; }
        public DbSet<GNM_WorkFlowParent> GNM_WorkFlowParent { get; set; }
        public DbSet<WF_WFChildActions> WF_WFChildActions { get; set; }
        public DbSet<WF_WFStepLink> WF_WFStepLink { get; set; }
        public DbSet<WF_Email> WF_Email { get; set; }
        public DbSet<WF_WFActionLocale> WF_WFActionLocale { get; set; }
        public DbSet<WF_WFStepsLocale> WF_WFStepsLocale { get; set; }
        public DbSet<WF_WFStepStatusLocale> WF_WFStepStatusLocale { get; set; }
        public DbSet<WF_WFRoleLocale> WF_WFRoleLocale { get; set; }
    }
    public partial class WF_WFStepStatusLocale
    {
        public int WFStepStatusLocale_ID { get; set; }
        public int WFStepStatus_ID { get; set; }
        public int Language_ID { get; set; }
        public string WFStepStatus_Nm { get; set; }
        public string StepStatusCode { get; set; }

        public virtual WF_WFStepStatus GNM_WFStepStatus { get; set; }
    }
    public partial class WF_WFRoleLocale
    {
        public int WFRoleLocale_ID { get; set; }
        public int WFRole_ID { get; set; }
        public string WFRole_Name { get; set; }
        public int Language_ID { get; set; }

        public virtual WF_WFRole GNM_WFRole { get; set; }
    }
    public partial class WF_WFStepsLocale
    {
        public int WFStepsLocale_ID { get; set; }
        public int WFSteps_ID { get; set; }
        public string WFStep_Name { get; set; }
        public int Language_ID { get; set; }

        public virtual WF_WFSteps GNM_WFSteps { get; set; }
    }
    public partial class WF_WFActionLocale
    {
        public int WFActionLocale_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public int WFAction_ID { get; set; }
        public int Language_ID { get; set; }
        public string WFAction_Name { get; set; }
        public string ActionCode { get; set; }

        public virtual WF_WFAction GNM_WFAction { get; set; }
        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }
    }
    public partial class WF_Email
    {
        public int Email_ID { get; set; }
        public string Email_Subject { get; set; }
        public string Email_Body { get; set; }
        public string Email_To { get; set; }
        public string Email_cc { get; set; }
        public string Email_Bcc { get; set; }
        public Nullable<System.DateTime> Email_Queue_Date { get; set; }
        public Nullable<System.DateTime> Email_Sent_Date { get; set; }
        public bool Email_SentStatus { get; set; }
        public string Email_Attachments { get; set; }
        public Nullable<bool> Email_IsError { get; set; }
        public Nullable<byte> NoOfAttempts { get; set; }
    }
    public partial class WF_WFChildActions
    {
        public int ChildActions_ID { get; set; }
        public int Object_ID { get; set; }
        public string Actions_Name { get; set; }
    }
    public partial class GNM_WorkFlowParent
    {
        public int WorkFlowParent_ID { get; set; }
        public Nullable<int> WorkFlow_ID { get; set; }
        public string TXN_Table { get; set; }
        public string Reference_Column { get; set; }
        public string Parent_Table { get; set; }
        public string Parent_Column { get; set; }
        public Nullable<byte> Parent_Level { get; set; }
        public Nullable<byte> Parent_WFID { get; set; }
        public string PrimaryKey_Column { get; set; }
    }
    public partial class WF_WFStepStatus
    {
        public WF_WFStepStatus()
        {
            this.GNM_WFSteps = new HashSet<WF_WFSteps>();
            this.GNM_WFStepStatusLocale = new HashSet<WF_WFStepStatusLocale>();
        }

        public int WFStepStatus_ID { get; set; }
        public string WFStepStatus_Nm { get; set; }
        public string StepStatusCode { get; set; }

        public virtual ICollection<WF_WFSteps> GNM_WFSteps { get; set; }
        public virtual ICollection<WF_WFStepStatusLocale> GNM_WFStepStatusLocale { get; set; }
    }
    public class CaseProgressObjects
    {
        public int RoleID
        {
            get;
            set;
        }

        public int CompanyID
        {
            get;
            set;
        }

        public int transactionNumber
        {
            get;
            set;
        }

        public int workFlowID
        {
            get;
            set;
        }

        public int currentStepID
        {
            get;
            set;
        }

        public DateTime receivedTime
        {
            get;
            set;
        }

        public int actionID
        {
            get;
            set;
        }

        public int actionBy
        {
            get;
            set;
        }

        public int AssignTo
        {
            get;
            set;
        }

        public string actionRemarks
        {
            get;
            set;
        }

        public byte addresseType
        {
            get;
            set;
        }

        public DateTime actionTime
        {
            get;
            set;
        }

        public string smsTextAddressee
        {
            get;
            set;
        }

        public string smsTextCustomer
        {
            get;
            set;
        }

        public string customerMobileNumber
        {
            get;
            set;
        }

        public string customerEmailID
        {
            get;
            set;
        }

        public string emailSubAddressee
        {
            get;
            set;
        }

        public string emailBodyAddress
        {
            get;
            set;
        }

        public string emailBodyCustomer
        {
            get;
            set;
        }

        public string emailSubCustomer
        {
            get;
            set;
        }

        public int NextStepID
        {
            get;
            set;
        }
        //added by Kavitha -start
        public string CustomerBcc
        {
            get;
            set;
        }
        public string customerCC
        {
            get;
            set;
        }
        public string AddresseBcc
        {
            get;
            set;
        }
        public string AddresseCC
        {
            get;
            set;
        }
        //added by Kavitha -end
    }
    public partial class WF_EmployeeBranch
    {
        public int EmployeeBranch_ID { get; set; }
        public int CompanyEmployee_ID { get; set; }
        public int Branch_ID { get; set; }
        public Nullable<bool> IsDefault { get; set; }
    
        public virtual WF_Branch GNM_Branch { get; set; }
        public virtual WF_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }
    public partial class WF_PrefixSuffix
    {
        public int PrefixSuffix_ID { get; set; }
        public int Company_ID { get; set; }
        public Nullable<int> Branch_ID { get; set; }
        public int Object_ID { get; set; }
        public int Start_Number { get; set; }
        public string Prefix { get; set; }
        public string Suffix { get; set; }
        public System.DateTime FromDate { get; set; }
        public System.DateTime ToDate { get; set; }
        public int ModifiedBY { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public Nullable<int> FinancialYear { get; set; }
        public Nullable<int> Company_FinancialYear_ID { get; set; }

        public virtual WF_Branch GNM_Branch { get; set; }
        public virtual WF_Company GNM_Company { get; set; }
        public virtual WF_Object GNM_Object { get; set; }
    }
    public partial class WF_Role
    {
        public WF_Role()
        {
            this.GNM_RoleObject = new HashSet<WF_RoleObject>();
            this.GNM_UserRole = new HashSet<WF_UserRole>();
        }
    
        public int Role_ID { get; set; }
        public int Company_ID { get; set; }
        public string Role_Name { get; set; }
    
        public virtual ICollection<WF_RoleObject> GNM_RoleObject { get; set; }
        public virtual ICollection<WF_UserRole> GNM_UserRole { get; set; }
    }
    public partial class WF_RoleObject
    {
        public int RoleObject_ID { get; set; }
        public int Role_ID { get; set; }
        public int Object_ID { get; set; }
        public bool RoleObject_Create { get; set; }
        public bool RoleObject_Read { get; set; }
        public bool RoleObject_Update { get; set; }
        public bool RoleObject_Delete { get; set; }
        public bool RoleObject_Print { get; set; }
        public bool RoleObject_Export { get; set; }
        public bool RoleObject_Import { get; set; }

        public virtual WF_Object GNM_Object { get; set; }
        public virtual WF_Role GNM_Role { get; set; }
    }
    public partial class WF_Object
    {
        public WF_Object()
        {
            this.GNM_PrefixSuffix = new HashSet<WF_PrefixSuffix>();
            this.GNM_RoleObject = new HashSet<WF_RoleObject>();
        }

        public int Object_ID { get; set; }
        public string Object_Name { get; set; }
        public string Read_Action { get; set; }
        public string Create_Action { get; set; }
        public string Update_Action { get; set; }
        public string Delete_Action { get; set; }
        public string Export_Action { get; set; }
        public string Print_Action { get; set; }
        public bool Object_IsActive { get; set; }
        public string Object_Description { get; set; }
        public string Import_Action { get; set; }
        public string Object_Type { get; set; }

        public virtual ICollection<WF_PrefixSuffix> GNM_PrefixSuffix { get; set; }
        public virtual ICollection<WF_RoleObject> GNM_RoleObject { get; set; }
    }
    public partial class WF_Company
    {
        public WF_Company()
        {
            this.GNM_Branch = new HashSet<WF_Branch>();
            this.GNM_RefMasterDetail2 = new HashSet<WF_RefMasterDetail>();
            this.GNM_CompanyEmployee = new HashSet<WF_CompanyEmployee>();
            this.GNM_PrefixSuffix = new HashSet<WF_PrefixSuffix>();
            this.GNM_User = new HashSet<WF_User>();
        }

        public int Company_ID { get; set; }
        public string Company_Name { get; set; }
        public string Company_ShortName { get; set; }
        public int Currency_ID { get; set; }
        public string Company_Address { get; set; }
        public string Company_Type { get; set; }
        public bool Company_Active { get; set; }
        public string Company_LogoName { get; set; }
        public Nullable<int> Company_Parent_ID { get; set; }
        public string Remarks { get; set; }
        public byte DefaultGridSize { get; set; }
        public Nullable<decimal> JobCardCushionHours { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public Nullable<int> CompanyTheme_ID { get; set; }
        public Nullable<int> QuotationValidity { get; set; }
        public string CompanyFont { get; set; }
        public Nullable<decimal> InventoryCarryingFactoy_Percentage { get; set; }
        public Nullable<int> OrderingCost { get; set; }

        public virtual ICollection<WF_Branch> GNM_Branch { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail1 { get; set; }
        public virtual ICollection<WF_RefMasterDetail> GNM_RefMasterDetail2 { get; set; }
        public virtual ICollection<WF_CompanyEmployee> GNM_CompanyEmployee { get; set; }
        public virtual ICollection<WF_PrefixSuffix> GNM_PrefixSuffix { get; set; }
        public virtual ICollection<WF_User> GNM_User { get; set; }
    }
    public partial class WF_PartyBranchAssociation
    {
        public int PartyBranch_ID { get; set; }
        public int Party_ID { get; set; }
        public int Branch_ID { get; set; }

        public virtual WF_Branch GNM_Branch { get; set; }
    }
    public partial class WF_Branch
    {
        public WF_Branch()
        {
            this.GNM_PartyBranchAssociation = new HashSet<WF_PartyBranchAssociation>();
            this.GNM_PrefixSuffix = new HashSet<WF_PrefixSuffix>();
            this.GNM_EmployeeBranch = new HashSet<WF_EmployeeBranch>();
        }

        public int Branch_ID { get; set; }
        public int Company_ID { get; set; }
        public string Branch_Name { get; set; }
        public string Branch_ShortName { get; set; }
        public string Branch_ZipCode { get; set; }
        public int Country_ID { get; set; }
        public int State_ID { get; set; }
        public string Branch_Phone { get; set; }
        public string Branch_Fax { get; set; }
        public bool Branch_HeadOffice { get; set; }
        public bool Branch_Active { get; set; }
        public string Branch_Address { get; set; }
        public string Branch_Location { get; set; }
        public string Branch_Email { get; set; }
        public string Branch_Mobile { get; set; }
        public Nullable<bool> Branch_External { get; set; }
        public Nullable<int> TimeZoneID { get; set; }
        public Nullable<int> Region_ID { get; set; }
        public Nullable<int> Currency_ID { get; set; }
        public Nullable<int> LanguageID { get; set; }
        public Nullable<byte> IsOverTimeDWM { get; set; }
        public Nullable<decimal> Yearly_Sales_Target { get; set; }
        public Nullable<decimal> Rework_Target { get; set; }
        public Nullable<decimal> Cust_Satisfaction_Target { get; set; }
        public Nullable<decimal> RO_with_Rework_Target { get; set; }
        public Nullable<decimal> RO_with_Cust_Satisfaction_Target { get; set; }
        public Nullable<int> DueDays { get; set; }

        public virtual WF_Company GNM_Company { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail { get; set; }
        public virtual ICollection<WF_PartyBranchAssociation> GNM_PartyBranchAssociation { get; set; }
        public virtual ICollection<WF_PrefixSuffix> GNM_PrefixSuffix { get; set; }
        public virtual ICollection<WF_EmployeeBranch> GNM_EmployeeBranch { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail1 { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail2 { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail21 { get; set; }
    }
    public partial class WF_Menu
    {
        public WF_Menu()
        {
            this.GNM_MenuLocale = new HashSet<WF_MenuLocale>();
        }
    
        public int Menu_ID { get; set; }
        public int Module_ID { get; set; }
        public string Menu_Description { get; set; }
        public Nullable<int> Parentmenu_ID { get; set; }
        public Nullable<int> Object_ID { get; set; }
        public string Menu_Path { get; set; }
        public byte Menu_SortOrder { get; set; }
        public bool Menu_IsActive { get; set; }
        public string Menu_IconName { get; set; }
    
        public virtual WF_Module GNM_Module { get; set; }
        public virtual ICollection<WF_MenuLocale> GNM_MenuLocale { get; set; }
    }
    public partial class WF_MenuLocale
    {
        public int MenuLocale_ID { get; set; }
        public int Language_ID { get; set; }
        public int Menu_ID { get; set; }
        public string Menu_Description { get; set; }

        public virtual WF_Menu GNM_Menu { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail { get; set; }
    }
    public partial class WF_ModuleLocale
    {
        public int ModuleLocale_ID { get; set; }
        public int Language_ID { get; set; }
        public int Module_ID { get; set; }
        public string Module_Description { get; set; }
        public string Module_IconName { get; set; }
    
        public virtual WF_Module GNM_Module { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail { get; set; }
    }
    public partial class WF_Module
    {
        public WF_Module()
        {
            this.GNM_Menu = new HashSet<WF_Menu>();
            this.GNM_ModuleLocale = new HashSet<WF_ModuleLocale>();
        }
    
        public int Module_ID { get; set; }
        public string Module_Description { get; set; }
        public bool Module_IsActive { get; set; }
        public byte Module_SortOrder { get; set; }
        public string Module_IconName { get; set; }
    
        public virtual ICollection<WF_Menu> GNM_Menu { get; set; }
        public virtual ICollection<WF_ModuleLocale> GNM_ModuleLocale { get; set; }
    }
public partial class WF_RefMasterDetail
    {
        public WF_RefMasterDetail()
        {
            this.GNM_Branch = new HashSet<WF_Branch>();
            this.GNM_Company = new HashSet<WF_Company>();
            this.GNM_Company1 = new HashSet<WF_Company>();
            this.GNM_CompanyEmployee = new HashSet<WF_CompanyEmployee>();
            this.GNM_CompanyEmployee1 = new HashSet<WF_CompanyEmployee>();
            this.GNM_CompanyEmployee2 = new HashSet<WF_CompanyEmployee>();
            this.GNM_MenuLocale = new HashSet<WF_MenuLocale>();
            this.GNM_ModuleLocale = new HashSet<WF_ModuleLocale>();
            this.GNM_User = new HashSet<WF_User>();
            this.GNM_Branch1 = new HashSet<WF_Branch>();
            this.GNM_Branch2 = new HashSet<WF_Branch>();
            this.GNM_Branch21 = new HashSet<WF_Branch>();
            this.GNM_CompanyEmployee3 = new HashSet<WF_CompanyEmployee>();
            this.GNM_UserLocale = new HashSet<WF_UserLocale>();
        }

        public int RefMasterDetail_ID { get; set; }
        public bool RefMasterDetail_IsActive { get; set; }
        public string RefMasterDetail_Short_Name { get; set; }
        public string RefMasterDetail_Name { get; set; }
        public Nullable<int> Company_ID { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public int RefMaster_ID { get; set; }
        public bool RefMasterDetail_IsDefault { get; set; }

        public virtual ICollection<WF_Branch> GNM_Branch { get; set; }
        public virtual ICollection<WF_Company> GNM_Company { get; set; }
        public virtual ICollection<WF_Company> GNM_Company1 { get; set; }
        public virtual WF_Company GNM_Company2 { get; set; }
        public virtual ICollection<WF_CompanyEmployee> GNM_CompanyEmployee { get; set; }
        public virtual ICollection<WF_CompanyEmployee> GNM_CompanyEmployee1 { get; set; }
        public virtual ICollection<WF_CompanyEmployee> GNM_CompanyEmployee2 { get; set; }
        public virtual ICollection<WF_MenuLocale> GNM_MenuLocale { get; set; }
        public virtual ICollection<WF_ModuleLocale> GNM_ModuleLocale { get; set; }
        public virtual ICollection<WF_User> GNM_User { get; set; }
        public virtual ICollection<WF_Branch> GNM_Branch1 { get; set; }
        public virtual ICollection<WF_Branch> GNM_Branch2 { get; set; }
        public virtual ICollection<WF_Branch> GNM_Branch21 { get; set; }
        public virtual ICollection<WF_CompanyEmployee> GNM_CompanyEmployee3 { get; set; }
        public virtual ICollection<WF_UserLocale> GNM_UserLocale { get; set; }
    }
public partial class WF_UserLocale
    {
        public int User_Locale_ID { get; set; }
        public int User_ID { get; set; }
        public int Language_ID { get; set; }
        public string User_Name { get; set; }
    
        public virtual WF_RefMasterDetail GNM_RefMasterDetail { get; set; }
        public virtual WF_User GNM_User { get; set; }
    }

    public partial class WF_UserRole
    {
        public int UserRole_ID { get; set; }
        public int User_ID { get; set; }
        public int Role_ID { get; set; }

        public virtual WF_Role GNM_Role { get; set; }
        public virtual WF_User GNM_User { get; set; }
    }
    // Additional models needed for the service
    public partial class WF_User
    {
        public WF_User()
        {
            this.GNM_UserRole = new HashSet<WF_UserRole>();
            this.GNM_UserLocale = new HashSet<WF_UserLocale>();
        }

        public int User_ID { get; set; }
        public string User_Name { get; set; }
        public string User_LoginID { get; set; }
        public string User_Password { get; set; }
        public bool User_IsActive { get; set; }
        public bool User_Locked { get; set; }
        public Nullable<int> User_LoginCount { get; set; }
        public Nullable<int> User_FailedCount { get; set; }
        public int Company_ID { get; set; }
        public int Language_ID { get; set; }
        public byte User_Type_ID { get; set; }
        public Nullable<int> Employee_ID { get; set; }
        public Nullable<int> Partner_ID { get; set; }
        public string LandingPage { get; set; }
        public string User_IPAddress { get; set; }
        public Nullable<int> WareHouse_ID { get; set; }
        public string ReleaseVersionPopup { get; set; }

        public virtual WF_Company GNM_Company { get; set; }
        public virtual WF_CompanyEmployee GNM_CompanyEmployee { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail { get; set; }
        public virtual ICollection<WF_UserRole> GNM_UserRole { get; set; }
        public virtual ICollection<WF_UserLocale> GNM_UserLocale { get; set; }
    }

    public partial class WF_CompanyEmployee
    {
        public WF_CompanyEmployee()
        {
            this.GNM_User = new HashSet<WF_User>();
            this.GNM_EmployeeBranch = new HashSet<WF_EmployeeBranch>();
        }
    
        public int Company_Employee_ID { get; set; }
        public string Employee_ID { get; set; }
        public string Company_Employee_Name { get; set; }
        public int Company_ID { get; set; }
        public int Country_ID { get; set; }
        public int State_ID { get; set; }
        public string Company_Employee_MobileNumber { get; set; }
        public string Company_Employee_Landline_Number { get; set; }
        public string Company_Employee_ZipCode { get; set; }
        public Nullable<System.DateTime> Company_Employee_ActiveFrom { get; set; }
        public Nullable<System.DateTime> Company_Employee_ValidateUpTo { get; set; }
        public bool Company_Employee_Active { get; set; }
        public Nullable<int> Company_Employee_Manager_ID { get; set; }
        public string Company_Employee_Address { get; set; }
        public string Company_Employee_Location { get; set; }
        public string Company_Employee_Email { get; set; }
        public int Company_Employee_Department_ID { get; set; }
        public int Company_Employee_Designation_ID { get; set; }
        public int ModifiedBy { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public Nullable<decimal> HourlyRate { get; set; }
        public Nullable<int> Region_ID { get; set; }
        public Nullable<bool> IsEligibleForOT { get; set; }
        public Nullable<byte> ExemptionHours { get; set; }
        public Nullable<bool> IsOnJob { get; set; }
        public Nullable<int> JobID { get; set; }
    
        public virtual WF_Company GNM_Company { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail1 { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail2 { get; set; }
        public virtual ICollection<WF_User> GNM_User { get; set; }
        public virtual ICollection<WF_EmployeeBranch> GNM_EmployeeBranch { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail3 { get; set; }
    }

    // GenEntities DbContext for general entities
    public partial class GenEntities : DbContext
    {
        public GenEntities(DbContextOptions<GenEntities> options) : base(options)
        {
        }

        public GenEntities(string connectionString) : base(GetOptions(connectionString))
        {
        }

        private static DbContextOptions<GenEntities> GetOptions(string connectionString)
        {
            var optionsBuilder = new DbContextOptionsBuilder<GenEntities>();
            optionsBuilder.UseSqlServer(connectionString);
            return optionsBuilder.Options;
        }

        public DbSet<WF_Branch> WF_Branch { get; set; }
        public DbSet<WF_Company> WF_Company { get; set; }
        public DbSet<WF_CompanyEmployee> WF_CompanyEmployee { get; set; }
        public DbSet<WF_Email> WF_Email { get; set; }
        public DbSet<WF_Menu> WF_Menu { get; set; }
        public DbSet<WF_MenuLocale> WF_MenuLocale { get; set; }
        public DbSet<WF_Module> WF_Module { get; set; }
        public DbSet<WF_ModuleLocale> WF_ModuleLocale { get; set; }
        public DbSet<WF_Object> WF_Object { get; set; }
        public DbSet<WF_PartyBranchAssociation> WF_PartyBranchAssociation { get; set; }
        public DbSet<WF_PrefixSuffix> WF_PrefixSuffix { get; set; }
        public DbSet<WF_RefMasterDetail> WF_RefMasterDetail { get; set; }
        public DbSet<WF_Role> WF_Role { get; set; }
        public DbSet<WF_RoleObject> WF_RoleObject { get; set; }
        public DbSet<WF_Sms> WF_Sms { get; set; }
        public DbSet<WF_User> WF_User { get; set; }
        public DbSet<WF_UserRole> WF_UserRole { get; set; }
        public DbSet<WF_EmployeeBranch> WF_EmployeeBranch { get; set; }
        public DbSet<WF_UserLocale> WF_UserLocale { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
        }


    }

    // Utility classes for logging and common functions
    public static class LogSheetExporter
    {
        public static void LogToTextFile(int errorCode, string errorType, string targetSite, string stackTrace)
        {
            try
            {
                // Implement logging logic here
                // For now, just log to console or use ILogger
                Console.WriteLine($"Error: {errorCode} - {errorType}");
                Console.WriteLine($"Target Site: {targetSite}");
                Console.WriteLine($"Stack Trace: {stackTrace}");
            }
            catch (Exception ex)
            {
                // Handle logging errors
                Console.WriteLine($"Logging error: {ex.Message}");
            }
        }
    }

    // Result class for action results
    public class ResultForAction
        {
            public int ID { get; set; }

            public string Name { get; set; }

            public bool isVersionEnabled { get; set; }
        }

    // Request model for GetRolesForActions
    public class GetRolesForActionsRequest
    {
        public int WFCurrentStepID { get; set; }
        public int ActionID { get; set; }
        public int TransactionID { get; set; }
        public int CompanyID { get; set; }
        public string WorkFlowName { get; set; } = string.Empty;
        public string DBName { get; set; } = string.Empty;
        public int UserLanguageID { get; set; }
        public string ConnectionString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    // Response model for GetRolesForActions
    public class GetRolesForActionsResponse
    {
        public bool Success { get; set; }
        public dynamic? Data { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    // Request and Response models for HTTP endpoints

    // Insert Workflow History Request/Response
    public class InsertWorkFlowHistoryRequest
    {
        public string? ConnectionString { get; set; }
        public CaseProgressObjects CPDetails { get; set; } = new();
        public SMSTemplate SMSCustomerObj { get; set; } = new();
        public SMSTemplate SMSAssigneeObj { get; set; } = new();
        public int BranchID { get; set; } = 0;
    }

    // Check Invoke Child Object Request/Response
    public class CheckInvokeChildObjectRequest
    {
        public int CompanyID { get; set; }
        public int WorkFlowID { get; set; }
        public int StepID { get; set; }
        public int ActionID { get; set; }
        public int ToStepID { get; set; }
    }

    public class CheckInvokeChildObjectResponse
    {
        public bool Success { get; set; }
        public bool HasChildObject { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    // Invoke Child Action Request/Response
    public class InvokeChildActionRequest
    {
        public int StepLinkID { get; set; }
    }

    public class InvokeChildActionResponse
    {
        public bool Success { get; set; }
        public string ObjectName { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }

    // Local Time Request/Response
    public class LocalTimeRequest
    {
        public string? ConnectionString { get; set; }
        public int BranchID { get; set; }
        public DateTime ServerTime { get; set; }
    }

    public class LocalTimeResponse
    {
        public bool Success { get; set; }
        public DateTime LocalTime { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    // Generic Workflow API Response
    public class WorkflowAPIResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    // WorkflowCommon Request/Response Models

    // Check Prefix Suffix Request/Response
    public class CheckPrefixSuffixRequest
    {
        [Required]
        public int CompanyID { get; set; }

        [Required]
        public int BranchID { get; set; }

        [Required]
        public string ObjectName { get; set; } = string.Empty;

        [Required]
        public string DbName { get; set; } = string.Empty;

        [Required]
        public string ConnectionString { get; set; } = string.Empty;
    }

    public class CheckPrefixSuffixResponse
    {
        public bool Success { get; set; }
        public bool HasPrefixSuffix { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    // Get Object ID Request/Response
    public class GetObjectIDRequest
    {
        [Required]
        public string Name { get; set; } = string.Empty;

        [Required]
        public string DbName { get; set; } = string.Empty;

        [Required]
        public string ConnectionString { get; set; } = string.Empty;
    }

    public class GetObjectIDResponse
    {
        public bool Success { get; set; }
        public int ObjectID { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}