using System.Data.SqlClient;

namespace PBC.UtilityService.Models
{
    public class GroupQueueRequest
    {
        public int WorkFlowID { get; set; }
        public int UserID { get; set; }
        public int CompanyID { get; set; }
        public string ConnString { get; set; }
        public int LogException { get; set; }
    }

    public class GroupQueueAdvancedRequest
    {
        public int CompanyID { get; set; }
        public string ConnString { get; set; }
        public int WorkFlowID { get; set; }
        public int UserID { get; set; }
    }

    public class AllQueueRequest
    {
        public int WorkFlowID { get; set; }
        public int UserID { get; set; }
        public int CompanyID { get; set; }
        public string ConnString { get; set; }
        public int LogException { get; set; }
        public int StatusID { get; set; }
        public int BranchID { get; set; }
    }

    public class CheckAddRecordsRequest
    {
        public int ObjectID { get; set; }
        public int WorkFlowID { get; set; }
        public int CompanyID { get; set; }
        public int UserID { get; set; }
        public string ConnString { get; set; }
        public int LogException { get; set; }
    }

    public class GetValueFromDBRequest
    {
        public string Query { get; set; }
        public List<SqlParameter> Parameters { get; set; }
        public string ConnectionString { get; set; }
        public int LogException { get; set; }
        public bool NextResult { get; set; } = false;
        public int ResultSetIndex { get; set; } = 0;
        public bool IsStoredProcedure { get; set; } = false;
        public Type ReturnType { get; set; }
    }

    public class GetWorkFlowIDRequest
    {
        public string WorkFlowName { get; set; }
        public string DBName { get; set; }
        public string ConnString { get; set; }
        public int LogException { get; set; }
    }

    public class InitialSetupRequest
    {
        public int ObjectId { get; set; }
        public int UserId { get; set; }
        public string ConnectionString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    public class ValidateCalldateandPCDRequest
    {
        public DateTime PCD { get; set; }
        public DateTime Calldate { get; set; }
    }

    public class CheckBayWorkshopAvailabilityRequest
    {
        public DateTime ExpectedArrivalDate { get; set; }
        public DateTime ExpectedDepartureDate { get; set; }
        public bool IsWIPBay { get; set; }
        public int BookingMinutes { get; set; }
        public int ServiceRequest_ID { get; set; }
        public int Quotation_ID { get; set; }
        public int Branch { get; set; }
        public string ConnectionString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    public class CheckAutoAllocationRequest
    {
        public int CompanyID { get; set; }
        public int WorkFlowID { get; set; }
        public int UserID { get; set; }
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    public class GetAutoAllocationStepDetailsRequest
    {
        public int WorkFlowID { get; set; }
        public int CompanyID { get; set; }
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    public class LockRecordRequest
    {
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
        public string UserCulture { get; set; } = string.Empty;
        public int QuotationID { get; set; }
        public int UserID { get; set; }
        public int CompanyID { get; set; }
        public string WorkFlowName { get; set; } = string.Empty;
        public string DBName { get; set; } = string.Empty;
        public int Branch_ID { get; set; } = 0;
    }

    public class UnLockRecordRequest
    {
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; }
        public string UserCulture { get; set; } = string.Empty;
        public int JobcardID { get; set; }
        public int UserID { get; set; }
        public int CompanyID { get; set; }
        public string WorkFlowName { get; set; } = string.Empty;
        public string DBName { get; set; } = string.Empty;
        public int Branch_ID { get; set; } = 0;
    }

    public class LocalTimeBasedOnBranchRequest
    {
        public int BranchID { get; set; }
        public DateTime ServerTime { get; set; }
        public string ConnString { get; set; } = string.Empty;
    }

    public class LocalTimeRequest
    {
        public int UserID { get; set; }
        public DateTime ServerTime { get; set; }
        public string ConnString { get; set; } = string.Empty;
    }

    public class GetEndStepStatusNameRequest
    {
        public int WorkflowID { get; set; }
        public string ConnString { get; set; } = string.Empty;
        public int LogException { get; set; } = 1;
    }
}
