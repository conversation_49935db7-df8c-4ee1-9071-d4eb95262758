using System.Collections.Generic;

namespace PBC.UtilityService.Utilities.Models
{
    /// <summary>
    /// Represents a GNM Object entity
    /// </summary>
    public class GNM_Object
    {
        /// <summary>
        /// Gets or sets the Object ID
        /// </summary>
        public int Object_ID { get; set; }

        /// <summary>
        /// Gets or sets the Object Name
        /// </summary>
        public string Object_Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Read Action
        /// </summary>
        public string Read_Action { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Create Action
        /// </summary>
        public string Create_Action { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Update Action
        /// </summary>
        public string Update_Action { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Delete Action
        /// </summary>
        public string Delete_Action { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Export Action
        /// </summary>
        public string Export_Action { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Print Action
        /// </summary>
        public string Print_Action { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets whether the object is active
        /// </summary>
        public bool Object_IsActive { get; set; }

        /// <summary>
        /// Gets or sets the Object Description
        /// </summary>
        public string Object_Description { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Import Action
        /// </summary>
        public string Import_Action { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Object Type
        /// </summary>
        public string Object_Type { get; set; } = string.Empty;
    }
}
