<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>PBC.WorkflowService</RootNamespace>
    <AssemblyName>PBC.WorkflowService</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="9.0.6" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="7.0.3" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.0.3" />
    <PackageReference Include="AngleSharp" Version="0.17.1" />
    <PackageReference Include="AWSSDK.Core" Version="3.7.302.3" />
    <PackageReference Include="AWSSDK.S3" Version="3.7.305.19" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.19.1" />
    <PackageReference Include="ClosedXML" Version="0.102.2" />
    <PackageReference Include="EPPlus" Version="7.4.1" />
    <PackageReference Include="ExcelDataReader" Version="3.6.0" />
    <PackageReference Include="FastMember" Version="1.5.0" />
    <PackageReference Include="HtmlSanitizer" Version="8.0.795" />
    <PackageReference Include="iTextSharp" Version="5.5.13.3" />
    <PackageReference Include="PuppeteerSharp" Version="20.0.4" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.4" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.1.1">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

</Project>
