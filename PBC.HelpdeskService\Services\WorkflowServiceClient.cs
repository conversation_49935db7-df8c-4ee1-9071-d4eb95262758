using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;

namespace PBC.HelpdeskService.Services
{
    /// <summary>
    /// Client for communicating with PBC.WorkflowService
    /// </summary>
    public class WorkflowServiceClient : IWorkflowServiceClient
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<WorkflowServiceClient> _logger;
        private readonly string _workflowServiceUrl;

        public WorkflowServiceClient(HttpClient httpClient, IConfiguration configuration, ILogger<WorkflowServiceClient> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _logger = logger;
            _workflowServiceUrl = _configuration["ServiceUrls:WorkflowService"] ?? "http://localhost:5005";
        }

        /// <inheritdoc/>
        public async Task<bool> CheckPrefixSuffixAsync(int companyID, int branchID, string objectName, string dbName, string connectionString)
        {
            try
            {
                _logger.LogInformation("Calling workflow service to check prefix/suffix for Company: {CompanyID}, Branch: {BranchID}, Object: {ObjectName}", 
                    companyID, branchID, objectName);

                var request = new CheckPrefixSuffixRequest
                {
                    CompanyID = companyID,
                    BranchID = branchID,
                    ObjectName = objectName,
                    DbName = dbName,
                    ConnectionString = connectionString
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_workflowServiceUrl}/api/workflowapi/check-prefix-suffix", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    var responseObj = JsonConvert.DeserializeObject<CheckPrefixSuffixResponse>(result);
                    return responseObj?.HasPrefixSuffix ?? false;
                }
                else
                {
                    _logger.LogWarning("Failed to check prefix/suffix. Status: {StatusCode}", response.StatusCode);
                    return false; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling workflow service to check prefix/suffix");
                return false; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<int> GetObjectIDAsync(string name, string dbName, string connectionString)
        {
            try
            {
                _logger.LogInformation("Calling workflow service to get object ID for Name: {Name}, DB: {DbName}", name, dbName);

                var request = new GetObjectIDRequest
                {
                    Name = name,
                    DbName = dbName,
                    ConnectionString = connectionString
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_workflowServiceUrl}/api/workflowapi/get-object-id", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    var responseObj = JsonConvert.DeserializeObject<GetObjectIDResponse>(result);
                    return responseObj?.ObjectID ?? 0;
                }
                else
                {
                    _logger.LogWarning("Failed to get object ID. Status: {StatusCode}", response.StatusCode);
                    return 0; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling workflow service to get object ID");
                return 0; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<dynamic> GetRolesForActionsAsync(int wfCurrentStepID, int actionID, int transactionID, int companyID, string workFlowName, string dbName, int userLanguageID, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Calling workflow service to get roles for actions - WFCurrentStepID: {WFCurrentStepID}, ActionID: {ActionID}, CompanyID: {CompanyID}",
                    wfCurrentStepID, actionID, companyID);

                var request = new GetRolesForActionsRequest
                {
                    WFCurrentStepID = wfCurrentStepID,
                    ActionID = actionID,
                    TransactionID = transactionID,
                    CompanyID = companyID,
                    WorkFlowName = workFlowName,
                    DBName = dbName,
                    UserLanguageID = userLanguageID,
                    ConnectionString = connectionString,
                    LogException = logException
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_workflowServiceUrl}/api/workflowcommon/get-roles-for-actions", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    var responseObj = JsonConvert.DeserializeObject<GetRolesForActionsResponse>(result);
                    return responseObj?.Data ?? new { };
                }
                else
                {
                    _logger.LogWarning("Failed to get roles for actions. Status: {StatusCode}", response.StatusCode);
                    return new { }; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling workflow service to get roles for actions");
                return new { }; // Fallback
            }
        }
    }

    // Request/Response models for WorkflowService communication
    public class CheckPrefixSuffixRequest
    {
        public int CompanyID { get; set; }
        public int BranchID { get; set; }
        public string ObjectName { get; set; } = string.Empty;
        public string DbName { get; set; } = string.Empty;
        public string ConnectionString { get; set; } = string.Empty;
    }

    public class CheckPrefixSuffixResponse
    {
        public bool Success { get; set; }
        public bool HasPrefixSuffix { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class GetObjectIDRequest
    {
        public string Name { get; set; } = string.Empty;
        public string DbName { get; set; } = string.Empty;
        public string ConnectionString { get; set; } = string.Empty;
    }

    public class GetObjectIDResponse
    {
        public bool Success { get; set; }
        public int ObjectID { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class GetRolesForActionsRequest
    {
        public int WFCurrentStepID { get; set; }
        public int ActionID { get; set; }
        public int TransactionID { get; set; }
        public int CompanyID { get; set; }
        public string WorkFlowName { get; set; } = string.Empty;
        public string DBName { get; set; } = string.Empty;
        public int UserLanguageID { get; set; }
        public string ConnectionString { get; set; } = string.Empty;
        public int LogException { get; set; }
    }

    public class GetRolesForActionsResponse
    {
        public bool Success { get; set; }
        public dynamic? Data { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
