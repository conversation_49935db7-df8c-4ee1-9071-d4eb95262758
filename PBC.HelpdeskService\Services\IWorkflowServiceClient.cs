namespace PBC.HelpdeskService.Services
{
    /// <summary>
    /// Interface for communicating with PBC.WorkflowService
    /// </summary>
    public interface IWorkflowServiceClient
    {
        /// <summary>
        /// Check if prefix/suffix configuration exists for the given parameters using workflow service
        /// </summary>
        /// <param name="companyID">Company ID</param>
        /// <param name="branchID">Branch ID</param>
        /// <param name="objectName">Object name to check</param>
        /// <param name="dbName">Database name</param>
        /// <param name="connectionString">Database connection string</param>
        /// <returns>True if prefix/suffix configuration exists, false otherwise</returns>
        Task<bool> CheckPrefixSuffixAsync(int companyID, int branchID, string objectName, string dbName, string connectionString);

        /// <summary>
        /// Get object ID by object name using workflow service
        /// </summary>
        /// <param name="name">Object name</param>
        /// <param name="dbName">Database name</param>
        /// <param name="connectionString">Database connection string</param>
        /// <returns>Object ID if found, 0 otherwise</returns>
        Task<int> GetObjectIDAsync(string name, string dbName, string connectionString);

        /// <summary>
        /// Get roles for actions in workflow step using workflow service
        /// </summary>
        /// <param name="wfCurrentStepID">Current workflow step ID</param>
        /// <param name="actionID">Action ID</param>
        /// <param name="transactionID">Transaction ID</param>
        /// <param name="companyID">Company ID</param>
        /// <param name="workFlowName">Workflow name</param>
        /// <param name="dbName">Database name</param>
        /// <param name="userLanguageID">User language ID</param>
        /// <param name="connectionString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <returns>Dynamic result containing roles and individuals for actions</returns>
        Task<dynamic> GetRolesForActionsAsync(int wfCurrentStepID, int actionID, int transactionID, int companyID, string workFlowName, string dbName, int userLanguageID, string connectionString, int logException);
    }
}
