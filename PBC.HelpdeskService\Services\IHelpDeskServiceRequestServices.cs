﻿using Microsoft.AspNetCore.Mvc;
using PBC.HelpdeskService.Models;

namespace PBC.HelpdeskService.Services
{
    public interface IHelpDeskServiceRequestServices
    {
        Task<IActionResult> InsertServiceRequestNegativeFeedbackEmailsAsync(InsertServiceRequestNegativeFeedbackEmailsList Obj, string constring, int LogException);

        // Service Request Functions
        Task<JsonResult> InsertSR(string constring, string Reopen, string Data, int BranchID, string UserLanguageCode, List<string> RequestParams,
            List<Attachements> HDAttachmentData, string Path, string connString, int LogException, int Company_ID, int User_ID
            , string HelpDesk, string HelpLineNumber, int Language_ID, int Employee_ID, int MenuID, string HolidayDetails
            , bool isFromWebAPI = false);

        JsonResult InitialMode(string userEmployeeId, int companyId, string userCulture, string connectionString,
            string? unRegServiceRequestId = null, string? partyId = null, string? modelId = null, string? serialNumber = null,
            string? requestDesc = null, string? modelName = null, string? unique = null, string? serviceRequestId = null,
            string? reopen = null, int? mode = null, int? statusId = null, int? companyIdAlt = null);

        object SelectSRDetails(GNM_User user, int serviceRequestID, int childTicketSequenceID, int branchID, string connectionString,
            bool isReopen, bool isOemDashboard, int? oemCompanyId, string userCulture, string generalLanguageCode,
            string userLanguageCode, int menuID, DateTime loggedInDateTime);

        JsonResult GetProductDetails(GetProductDetailsFList Obj, string constring, int LogException);

        Task<JsonResult> GetActions(GetActionsList Obj, string constring, int LogException);

        JsonResult EditSR(string Data, int BranchID, string UserLanguageCode, List<string> RequestParams,
             List<Attachements> HDAttachmentData, int LoginComapnyID, string connString, int LogException, int Company_ID, int User_ID
            , string HelpDesk, string HelpLineNumber, int Language_ID, int Employee_ID, int MenuID, string HolidayDetails
            , bool isFromWebAPI = false);

        Task<JsonResult> GetSRDetails(GNM_User user, int ServiceRequestID, int ChildTicket_Sequence_ID, int User_ID, int Company_ID,
            int Language_ID, string UserCulture, string ROPN, string connectionString, string ServiceRequestNumber = "",
            string GenLangCode = "en", string UserLangCode = "en");

        CallDateDetail GetDetails(DateTime? Calldate, int companyID, string connString, int LogException);

        JsonResult loadMasters(loadMastersList Obj, string constring, int LogException);

        JsonResult loadIssueSubArea(loadIssueSubAreaList Obj, string constring, int LogException);

        JsonResult GetPartyDetailsbyID(GetPartyDetailsbyIDEList Obj, string constring, int LogException);

        JsonResult SelectPartyDetailGrid(SelectPartyDetailGridEList Obj, string constring, int LogException, string sidx, string sord, int page, int rows);

        JsonResult getProductUniqueNumber(getProductUniqueNumberFList Obj, string constring, int LogException);

        JsonResult getProductWarranty(getProductWarrantyList Obj, string contring, int LogException);

        JsonResult ValidateReading(ValidateReadingList Obj, string constring, int LogException);

        JsonResult ValidateSerialNumber(ValidateSerialNumberList Obj, string constring, int LogException);

        JsonResult getBrandProductType(getBrandProductTypecList Obj, string constring, int LogException);

        JsonResult GetSerialNumberForModelforDealer(GetSerialNumberForModelforDealerList Obj, string constring, int LogException);

        JsonResult getCustomerDetailsByPhone(getCustomerDetailsByPhoneList Obj, string constring, int LogException);

        JsonResult getCustomerDetailsByEmail(getCustomerDetailsByEmailCList Obj, string constring, int LogException);

        JsonResult ContactPersonMasterSave(ContactPersonMasterSaveList Obj, string constring, int LogException);

        JsonResult GetAllProductDetailsForDealer(GetAllProductDetailsForDealerList Obj, string constring, int LogException);

        Task<IActionResult> GetRolesForActions(GetRolesForActionsList Obj, string constring, int LogException, string HelpDesk);

        JsonResult GetMovementofWorkFlow(GetMovementofWorkFlowList Obj, string constring, int LogException, string HelpDesk, string sidx, string sord, int page, int rows);

        JsonResult GetMovementofWorkFlowforAll(GetMovementofWorkFlowforAllList Obj, string constring, int LogException, string HelpDesk, string sidx, string sord, int page, int rows);

        JsonResult SelHDProductDetails(SelHDProductDetailsList Obj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters);

        JsonResult GetScheduledDropins(GetScheduledDropinsList Obj, string constring, int LogException);

        JsonResult SelectAllDropdownData(SelectAllDropdownDataList Obj, string constring, int LogException);

        Task<JsonResult> SelectFieldSearchName(SelectFieldSearchNameList Obj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters, bool advnce, string Query);

        JsonResult GetContactPersonDetails(GetContactPersonDetailsList Obj, string constring, int LogException);

        JsonResult SelectMultiplePartPrefix(SelectMultiplePartPrefixList Obj, string constring, int LogException);

        JsonResult CheckRequestPart(CheckRequestPartList Obj, string constring, int LogException);

        JsonResult SelectFieldSearchPart(SelectFieldSearchPartList Obj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters, bool advnce, string Query);

        JsonResult GetSecondarySegment(GetSecondarySegmentList Obj, string constring, int LogException);

        JsonResult GetPrimarySegment_LostSalesReasons(GetPrimarySegment_LostSalesReasonsList Obj, string constring, int LogException);

        JsonResult SelectFieldSearchSerialNumber(SelectFieldSearchSerialNumberList Obj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters, bool advnce, string Query);

        JsonResult GetAllocationDetails(GetAllocationDetailsList Obj, string constring, int LogException);

        JsonResult GetProductDetailsOnSerial(GetProductDetailsOnSerialList Obj, string constring, int LogException);

        JsonResult GetEndStep(GetEndStepList Obj, string constring, int LogException, string HelpDesk);

        JsonResult GetProductIDOnModelBrandProductType(GetProductIDOnModelBrandProductTypeList Obj, string constring, int LogException);

        JsonResult UpdatePartyContactDetails(UpdatePartyContactDetailsList Obj, string constring, int LogException);

        JsonResult GetDepartmentandNameData(GetDepartmentandNameDataList Obj, string constring, int LogException);

        JsonResult SelServiceRequestNavigation(SelServiceRequestNavigationList Obj, string constring, int LogException, string HelpDesk);

        JsonResult Insert(ServiceRequest_SelectList Obj, string constring, int LogException);

        JsonResult Edit(ServiceRequest_SelectList Obj, string constring, int LogException);

        JsonResult UpdateDesc(UpdateDescList Obj, string constring, int LogException);

        JsonResult getSerialNumberForModel(getSerialNumberForModelFList Obj, string constring, int LogException);

        JsonResult ProductMasterSave(ProductMasterSaveList Obj, string constring, int LogException);

        JsonResult ProductMasterSaveConfirm(ProductMasterSaveConfirmFList Obj, string constring, int LogException);

        JsonResult SelectModel(HD_SelectModelList Obj, string constring, int LogException);

        JsonResult SelectFieldSearchModel(SelectFieldSearchModelList Obj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters);
    }
}
