﻿using Microsoft.AspNetCore.Mvc;
// using PBC.Core_SharedAPIClass.Models;
using PBC.CoreService.Models;
using System.Text;

namespace PBC.CoreService.Services
{
    public interface ICoreEmailTemplateService
    {


        /// <summary>
        /// Check if old password matches the stored password
        /// </summary>
        /// <param name="obj">CheckOldPasswordList object</param>
        /// <param name="connString">Database connection string</param>
        /// <returns>JsonResult with count (1 if password matches, 0 if not)</returns>
        StringBuilder[] CommonMethodForEmailandSMS(string connString, CommonMethodForEmailandSMSList CommonMethodForEmailandSMSobj);

    }
}
